import 'package:community_material_icon/community_material_icon.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/static_methods.dart';

//寫死一份下拉選單時間列表
const List<TimeOfDay> dropDownTimeOfDayList = [
  TimeOfDay(hour: 0, minute: 0),  TimeOfDay(hour: 0, minute: 30),
  TimeOfDay(hour: 1, minute: 0),  TimeOfDay(hour: 1, minute: 30),
  TimeOfDay(hour: 2, minute: 0),  TimeOfDay(hour: 2, minute: 30),
  TimeOfDay(hour: 3, minute: 0),  TimeOfDay(hour: 3, minute: 30),
  TimeOfDay(hour: 4, minute: 0),  TimeOfDay(hour: 4, minute: 30),
  TimeOfDay(hour: 5, minute: 0),  TimeOfDay(hour: 5, minute: 30),
  TimeOfDay(hour: 6, minute: 0),  TimeOfDay(hour: 6, minute: 30),
  TimeOfDay(hour: 7, minute: 0),  TimeOfDay(hour: 7, minute: 30),
  TimeOfDay(hour: 8, minute: 0),  TimeOfDay(hour: 8, minute: 30),
  TimeOfDay(hour: 9, minute: 0),  TimeOfDay(hour: 9, minute: 30),
  TimeOfDay(hour: 10, minute: 0),  TimeOfDay(hour: 10, minute: 30),
  TimeOfDay(hour: 11, minute: 0),  TimeOfDay(hour: 11, minute: 30),
  TimeOfDay(hour: 12, minute: 0),  TimeOfDay(hour: 12, minute: 30),
  TimeOfDay(hour: 13, minute: 0),  TimeOfDay(hour: 13, minute: 30),
  TimeOfDay(hour: 14, minute: 0),  TimeOfDay(hour: 14, minute: 30),
  TimeOfDay(hour: 15, minute: 0),  TimeOfDay(hour: 15, minute: 30),
  TimeOfDay(hour: 16, minute: 0),  TimeOfDay(hour: 16, minute: 30),
  TimeOfDay(hour: 17, minute: 0),  TimeOfDay(hour: 17, minute: 30),
  TimeOfDay(hour: 18, minute: 0),  TimeOfDay(hour: 18, minute: 30),
  TimeOfDay(hour: 19, minute: 0),  TimeOfDay(hour: 19, minute: 30),
  TimeOfDay(hour: 20, minute: 0),  TimeOfDay(hour: 20, minute: 30),
  TimeOfDay(hour: 21, minute: 0),  TimeOfDay(hour: 21, minute: 30),
  TimeOfDay(hour: 22, minute: 0),  TimeOfDay(hour: 22, minute: 30),
  TimeOfDay(hour: 23, minute: 0),  TimeOfDay(hour: 23, minute: 30),
  TimeOfDay(hour: 24, minute: 0),
];

//右方按鈕顯示模式
enum OpenHourEditItemRightButtonMode { Add, Remove }

//設定功能用參數
class OpenHourEditItemArgs {
  OpenHourEditItemArgs(
      {this.mainTime,
        this.onMainTimeChanged,
        this.secondaryTime,
        this.onSecondaryTimeChanged,
        this.rightButtonMode,
        this.onRightButtonPressed,});

  //主要輸入框
  final String mainTime;
  final Function onMainTimeChanged;

  //次要輸入框
  final String secondaryTime;
  final Function onSecondaryTimeChanged;

  //右邊的按鈕顯示模式
  final OpenHourEditItemRightButtonMode rightButtonMode;
  //注意這邊可以客製化為傳出當前編輯好的字串讓外面方便作業
  final Function(String, String) onRightButtonPressed;
}

class OpenHourEditItem extends StatefulWidget {
  OpenHourEditItem(this.args, {Key key}) : super(key: key);

  final OpenHourEditItemArgs args;

  @override
  _OpenHourEditItemState createState() => _OpenHourEditItemState();
}

class _OpenHourEditItemState extends State<OpenHourEditItem> {

  String mainTimeValue;
  String secondaryTimeValue;

  @override
  void initState() {
    mainTimeValue = widget.args.mainTime;
    secondaryTimeValue = widget.args.secondaryTime;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant OpenHourEditItem oldWidget) {
    mainTimeValue = widget.args.mainTime;
    secondaryTimeValue = widget.args.secondaryTime;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> rowChildren = [];

    //Main drop down
    TimeOfDay mainTimeOfDateValue = tryParseToTimeOfDay(mainTimeValue);
    rowChildren.add(Expanded(
      child: DropdownButton<TimeOfDay>(
        isExpanded: true,
        value: mainTimeOfDateValue,
        onChanged: (TimeOfDay timeOfDay) {
          setState(() {
            mainTimeValue = timeOfDayFormattedStr(timeOfDay);
            // print('On Changed: ' + mainTimeValue);
            widget.args.onMainTimeChanged?.call(timeOfDayFormattedStr(timeOfDay));

            //這邊要防止右方選項選擇了範圍限制範圍以外的時間
            TimeOfDay secondaryTimeOfDateValue = tryParseToTimeOfDay(secondaryTimeValue);
            TimeOfDay earliestCloseTime = calculatedEarliestCloseTime();
            if (secondaryTimeOfDateValue == null || secondaryTimeOfDateValue.hour < earliestCloseTime.hour ||
                (secondaryTimeOfDateValue.hour < earliestCloseTime.hour && secondaryTimeOfDateValue.minute < earliestCloseTime.minute)) {
              //需要修正 secondaryTimeValue 至合法範圍
              secondaryTimeValue = timeOfDayFormattedStr(earliestCloseTime);
            }
          });
        },
        items: dropDownTimeOfDayList
            .map<DropdownMenuItem<TimeOfDay>>((TimeOfDay timeOfDay) {
          return DropdownMenuItem<TimeOfDay>(
            value: timeOfDay,
            child: Text(timeOfDayFormattedStr(timeOfDay), style: Get.textTheme.subtitle1,),
          );
        }).toList(),
        hint: Text('開門時間', style: Get.textTheme.subtitle1.copyWith(color: Colors.grey),),
      ),
    ));

    rowChildren.add(Padding(
      padding: EdgeInsets.symmetric(horizontal: 8),
      child: Icon(
        CommunityMaterialIcons.minus
      ),
    ));

    //Secondary drop down
    TimeOfDay secondaryTimeOfDateValue = tryParseToTimeOfDay(secondaryTimeValue);
    rowChildren.add(Expanded(
      child: DropdownButton<TimeOfDay>(
        isExpanded: true,
        value: secondaryTimeOfDateValue,
        onChanged: (TimeOfDay timeOfDay) {
          setState(() {
            secondaryTimeValue = timeOfDayFormattedStr(timeOfDay);
            // print('On Changed: ' + secondaryTimeValue);
            widget.args.onSecondaryTimeChanged?.call(timeOfDayFormattedStr(timeOfDay));
          });
        },
        items: closeTimeDropDownTimeOfDayList().map<DropdownMenuItem<TimeOfDay>>((TimeOfDay timeOfDay) {
          return DropdownMenuItem<TimeOfDay>(
            value: timeOfDay,
            child: Text(timeOfDayFormattedStr(timeOfDay), style: Get.textTheme.subtitle1),
          );
        }).toList(),
        hint: Text('關門時間', style: Get.textTheme.subtitle1.copyWith(color: Colors.grey),),
      ),
    ));

    //Right button
    //決定右方按鈕樣式
    Widget rightButtonIcon = Container();

    if (widget.args.rightButtonMode == OpenHourEditItemRightButtonMode.Add) {
      //新增按鈕
      rightButtonIcon = Container(
        padding: EdgeInsets.all(4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          border: Border.all(color: kColorPrimary, width: 2),
        ),
        child: Icon(
          Icons.add,
          size: 20,
          color: kColorPrimary,
        ),
      );
    } else if (widget.args.rightButtonMode == OpenHourEditItemRightButtonMode.Remove) {
      //移除按鈕
      rightButtonIcon = Container(
        padding: EdgeInsets.all(6),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(30),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.15),
                blurRadius: 5,
                spreadRadius: 5,
              )
            ]),
        child: Icon(
          Icons.close,
          size: 20,
          color: Colors.grey,
        ),
      );
    }
    rowChildren.add(InkWell(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: Center(
          child: rightButtonIcon,
        ),
      ),
      onTap: () async {
        //To do: pass some stuffs here.
        HapticFeedback.lightImpact();

        bool result = await widget.args.onRightButtonPressed
            ?.call(mainTimeValue, secondaryTimeValue);

        if (result) {
          //Clear input text when success.
          mainTimeValue = '';
          secondaryTimeValue = '';
        }
      },
    ));

    return Container(
      height: kMinInteractiveDimension,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: rowChildren,
      ),
    );
  }

  //給關門時間過濾選項用，嘗試參考已經設定好的 mainTime
  List<TimeOfDay> closeTimeDropDownTimeOfDayList() {
    if (mainTimeValue.isNotEmpty) {
      TimeOfDay mainTimeOfDateValue = tryParseToTimeOfDay(mainTimeValue);
      return dropDownTimeOfDayList.where((timeOfDay) {
        //比較 timeOfDay 與 mainTimeOfDateValue
        if (timeOfDay.hour > mainTimeOfDateValue.hour) {
          return true;
        }

        if (timeOfDay.hour == mainTimeOfDateValue.hour) {
          if (timeOfDay.minute >= mainTimeOfDateValue.minute) {
            return true;
          }
        }

        return false;
      }).toList();
    } else {
      //沒有設定主時間
      return dropDownTimeOfDayList;
    }
  }

  //當前最早的關門時間
  TimeOfDay calculatedEarliestCloseTime() {
    TimeOfDay mainTimeOfDateValue = tryParseToTimeOfDay(mainTimeValue);
    // print('calculatedEarliestCloseTime: [' + timeOfDayFormattedStr(mainTimeOfDateValue) + ']');
    for (int i = 0 ; i < dropDownTimeOfDayList.length - 1 ; i++) {
      if (dropDownTimeOfDayList[i].hour > mainTimeOfDateValue.hour) {
        return dropDownTimeOfDayList[i];
      }

      if (dropDownTimeOfDayList[i].hour == mainTimeOfDateValue.hour) {
        if (dropDownTimeOfDayList[i].minute >= mainTimeOfDateValue.minute) {
          return dropDownTimeOfDayList[i];
        }
      }
    }
    return dropDownTimeOfDayList.last;
  }

}